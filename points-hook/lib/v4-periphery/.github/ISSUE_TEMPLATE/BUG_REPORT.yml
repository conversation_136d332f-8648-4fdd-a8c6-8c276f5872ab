name: Bug report
description: File a bug report to help us improve the code
title: "[Bug]: "
labels: ["bug"]

body:
    - type: markdown
      attributes:
          value: |
              Please check that the bug is not already being tracked.
    - type: textarea
      attributes:
          label: Describe the bug
          description: Provide a clear and concise description of what the bug is and which contracts it affects.
      validations:
          required: true
    - type: textarea
      attributes:
          label: Expected Behavior
          description: Provide a clear and concise description of the desired fix.
      validations:
          required: true
    - type: textarea
      attributes:
          label: To Reproduce
          description: If you have written tests to showcase the bug, what can we run to reproduce the issue?
          placeholder: "git checkout <branchname> / forge test --match-test <testName>"
    - type: textarea
      attributes:
          label: Additional context
          description: If there is any additional context needed like a dependency or integrating contract that is affected please describe it below.



