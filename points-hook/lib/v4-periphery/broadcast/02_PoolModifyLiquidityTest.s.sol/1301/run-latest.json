{"transactions": [{"hash": "0x0c771496063ad199f07781ac8cdbafc67b38d67da61752abec27a5b14817d626", "transactionType": "CREATE", "contractName": "PoolModifyLiquidityTest", "contractAddress": "0x5fa728c0a5cfd51bee4b060773f50554c0c8a7ab", "function": null, "arguments": ["0x00B036B58a818B1BC34d502D3fE730Db729e62AC"], "transaction": {"from": "0x7024cc7e60d6560f0b5877da2bb921fcbf1f4375", "gas": "0x1ae5d8", "value": "0x0", "input": "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", "nonce": "0x121", "chainId": "0x515"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [{"status": "0x1", "cumulativeGasUsed": "0x166cfc", "logs": [], "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "type": "0x2", "transactionHash": "0x0c771496063ad199f07781ac8cdbafc67b38d67da61752abec27a5b14817d626", "transactionIndex": "0x4", "blockHash": "0x4f79ce27f7102b990f8708c532aeed6e29141a748a9c4417c7254e5e7e4d5d41", "blockNumber": "0x6c4358", "gasUsed": "0x14b0ce", "effectiveGasPrice": "0xf433c", "from": "0x7024cc7e60d6560f0b5877da2bb921fcbf1f4375", "to": null, "contractAddress": "0x5fa728c0a5cfd51bee4b060773f50554c0c8a7ab", "l1BaseFeeScalar": "0x7d0", "l1BlobBaseFee": "0x34", "l1BlobBaseFeeScalar": "0xdbba0", "l1Fee": "0x3878897372", "l1GasPrice": "0x9fa3283e", "l1GasUsed": "0xb0df"}], "libraries": [], "pending": [], "returns": {"testModifyRouter": {"internal_type": "contract PoolModifyLiquidityTest", "value": "0x5fa728C0A5cfd51BEe4B060773f50554c0C8A7AB"}}, "timestamp": 1733947585, "chain": 1301, "commit": "645fbc2e"}