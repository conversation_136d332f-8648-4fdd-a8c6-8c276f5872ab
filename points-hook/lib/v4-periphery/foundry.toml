[profile.default]
out = 'foundry-out'
solc_version = '0.8.26'
optimizer_runs = 44444444
via_ir = true
ffi = true
fs_permissions = [{ access = "read-write", path = ".forge-snapshots/" }, { access = "read", path = "foundry-out/" }]
evm_version = "cancun"
gas_limit = "3000000000"
fuzz_runs = 10_000
bytecode_hash = "none"

additional_compiler_profiles = [
  { name = "posm", via_ir = true, optimizer_runs = 30000 },
  { name = "descriptor", via_ir = true, optimizer_runs = 1 },
  { name = "test", via_ir = false }
]

compilation_restrictions = [
  { paths = "src/PositionManager.sol", optimizer_runs = 30000 },
  { paths = "src/PositionDescriptor.sol", optimizer_runs = 1 },
  { paths = "test/**", via_ir = false }
]

[profile.debug]
via_ir = false
optimizer_runs = 200
fuzz.runs = 100

[profile.ci]
fuzz_runs = 100_000

[profile.gas]
gas_limit=30_000_000

[rpc_endpoints]
sepolia = "https://rpc.sepolia.org"
unichain_sepolia = "https://sepolia.unichain.org"
base_sepolia = "https://sepolia.base.org"
arbitrum_sepolia = "https://sepolia-rollup.arbitrum.io/rpc"
mainnet = "https://mainnet.infura.io/v3/${INFURA_API_KEY}"
